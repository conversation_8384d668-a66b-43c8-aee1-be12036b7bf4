<template>
  <div class="workflow-define-canvas">
    <!-- 工具栏 -->
    <WorkflowToolbar :graph="graph" :loading="loading" @save="handleSave" @add-lane="handleAddLane" @generate-json="handleGenerateJson" />

    <!-- 主要内容区域 -->
    <div class="canvas-container">
      <!-- 左侧组件面板 -->
      <WorkflowStencil :graph="graph" :width="stencilWidth" @ready="handleStencilReady" @node-added="handleNodeAdded" />

      <!-- 中间画布区域 -->
      <WorkflowCanvas
        ref="canvasRef"
        :width="`calc(100% - ${stencilWidth + sidebarWidth}px)`"
        :height="'100%'"
        :data="swimlaneData"
        @ready="handleCanvasReady"
        @data-change="handleDataChange"
        @selection-change="handleSelectionChange"
        @node-click="handleNodeClick"
        @edge-click="handleEdgeClick"
        @blank-click="handleBlankClick"
      />

      <!-- 右侧属性面板 -->
      <WorkflowSidebar
        :selectedNode="selectedNode"
        :selectedEdge="selectedEdge"
        :canvasData="canvasData"
        :width="sidebarWidth"
        @node-properties-change="handleNodePropertiesChange"
        @edge-properties-change="handleEdgePropertiesChange"
        @canvas-properties-change="handleCanvasPropertiesChange"
        @width-change="handleSidebarWidthChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Graph } from '@antv/x6';
  import { Stencil } from '@antv/x6-plugin-stencil';

  // 导入组件
  import WorkflowToolbar from './WorkflowToolbar.vue';
  import WorkflowStencil from './WorkflowStencil.vue';
  import WorkflowCanvas from './WorkflowCanvas.vue';
  import WorkflowSidebar from './WorkflowSidebar.vue';

  // 导入 API 和类型
  import { queryById, saveOrUpdate } from '../list/WorkflowDefine.api';
  import { useWorkflowNodes } from './hooks/useWorkflowNodes';
  import type { WorkflowDefineData, SwimLaneData, NodePropertyData } from './types/workflow';

  const route = useRoute();
  const { createMessage } = useMessage();
  const { createLaneNode } = useWorkflowNodes();

  // 响应式数据
  const loading = ref(false);
  const graph = ref<Graph | null>(null);
  const stencil = ref<Stencil | null>(null);
  const canvasRef = ref();

  // 布局相关
  const stencilWidth = ref(200);
  const sidebarWidth = ref(300);

  // 选中状态
  const selectedNode = ref<any>(null);
  const selectedEdge = ref<any>(null);

  // 工作流定义数据
  const data = reactive<WorkflowDefineData>({
    id: '',
    businessKey: '',
    name: '',
    definitionView: '',
    isEffective: '',
    delFlag: '',
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    definition: '',
    nodes: '',
    startNodeId: '',
    endNodeId: '',
    edges: '',
    version: '',
  });

  // 泳道数据
  const swimlaneData = ref<SwimLaneData | null>(null);
  const canvasData = ref<any>(null);

  /**
   * 画布准备就绪事件
   */
  const handleCanvasReady = (graphInstance: Graph) => {
    graph.value = graphInstance;
    console.log('Canvas ready:', graphInstance);
  };

  /**
   * 组件面板准备就绪事件
   */
  const handleStencilReady = (stencilInstance: Stencil) => {
    stencil.value = stencilInstance;
    console.log('Stencil ready:', stencilInstance);
  };

  /**
   * 数据变化事件
   */
  const handleDataChange = (data: any) => {
    canvasData.value = data;
    console.log('Canvas data changed:', data);
  };

  /**
   * 选择变化事件
   */
  const handleSelectionChange = (selection: { nodes: any[]; edges: any[] }) => {
    console.log('Selection changed:', selection);
  };

  /**
   * 节点点击事件
   */
  const handleNodeClick = (node: any) => {
    selectedNode.value = node;
    selectedEdge.value = null;
    console.log('Node clicked:', node);
  };

  /**
   * 边点击事件
   */
  const handleEdgeClick = (edge: any) => {
    selectedEdge.value = edge;
    selectedNode.value = null;
    console.log('Edge clicked:', edge);
  };

  /**
   * 空白点击事件
   */
  const handleBlankClick = () => {
    selectedNode.value = null;
    selectedEdge.value = null;
    console.log('Blank clicked');
  };

  /**
   * 节点添加事件
   */
  const handleNodeAdded = (nodeData: any) => {
    console.log('Node added to stencil:', nodeData);
  };

  /**
   * 节点属性变化事件
   */
  const handleNodePropertiesChange = (properties: NodePropertyData) => {
    if (canvasRef.value && selectedNode.value) {
      canvasRef.value.updateSelectedNodeData(properties);
    }
    console.log('Node properties changed:', properties);
  };

  /**
   * 边属性变化事件
   */
  const handleEdgePropertiesChange = (properties: any) => {
    if (canvasRef.value && selectedEdge.value) {
      canvasRef.value.updateSelectedEdgeData(properties);
    }
    console.log('Edge properties changed:', properties);
  };

  /**
   * 画布属性变化事件
   */
  const handleCanvasPropertiesChange = (properties: any) => {
    Object.assign(data, properties);
    console.log('Canvas properties changed:', properties);
  };

  /**
   * 侧边栏宽度变化事件
   */
  const handleSidebarWidthChange = (width: number) => {
    sidebarWidth.value = width;
  };

  /**
   * 保存工作流
   */
  const handleSave = async () => {
    if (!graph.value) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    loading.value = true;
    try {
      // 获取画布数据
      const graphData = canvasRef.value?.getData();
      if (graphData) {
        // 更新定义视图数据
        data.definitionView = JSON.stringify({
          lanes: graphData.cells?.filter((cell: any) => cell.shape === 'lane') || [],
          nodes: graphData.cells?.filter((cell: any) => cell.shape && cell.shape !== 'lane' && !cell.source && !cell.target) || [],
          edges: graphData.cells?.filter((cell: any) => cell.source && cell.target) || [],
        });

        // 调用保存 API
        const isUpdate = !!data.id;
        const result = await saveOrUpdate(data, isUpdate);

        if (result.success) {
          createMessage.success('保存成功');
        } else {
          createMessage.error(result.message || '保存失败');
        }
      }
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      loading.value = false;
    }
  };

  /**
   * 添加泳道
   */
  const handleAddLane = () => {
    if (!graph.value) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    try {
      createLaneNode(graph.value as Graph);
      createMessage.success('泳道添加成功');
    } catch (error) {
      console.error('添加泳道失败:', error);
      createMessage.error('添加泳道失败');
    }
  };

  /**
   * 生成JSON
   */
  const handleGenerateJson = () => {
    if (!graph.value) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    try {
      const graphData = canvasRef.value?.getData();
      if (graphData) {
        const exportData = {
          nodes: graphData.cells?.filter((cell: any) => cell.shape && !cell.source && !cell.target) || [],
          edges: graphData.cells?.filter((cell: any) => cell.source && cell.target) || [],
        };

        const jsonString = JSON.stringify(exportData, null, 2);
        console.log('导出的JSON数据：', jsonString);
        createMessage.success('JSON数据已输出到控制台');
      }
    } catch (error) {
      console.error('生成JSON失败:', error);
      createMessage.error('生成JSON失败');
    }
  };
  /**
   * 加载工作流数据
   */
  const loadData = async () => {
    if (!route.params.id) {
      console.warn('No workflow ID provided');
      return;
    }

    loading.value = true;
    try {
      const res = await queryById({ id: route.params.id });
      if (res) {
        Object.assign(data, res);

        // 解析定义视图数据
        if (data.definitionView) {
          try {
            const parsedData = JSON.parse(data.definitionView);
            swimlaneData.value = parsedData;
          } catch (error) {
            console.error('解析定义视图数据失败:', error);
            createMessage.error('工作流数据格式错误');
          }
        }
      }
    } catch (error) {
      console.error('加载工作流数据失败:', error);
      createMessage.error('加载工作流数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 生命周期函数
  onMounted(() => {
    // 加载数据
    loadData();
  });

  onUnmounted(() => {
    // 清理资源
    if (graph.value) {
      graph.value = null;
    }
    if (stencil.value) {
      stencil.value = null;
    }
  });
</script>

<style lang="less" scoped>
  .workflow-define-canvas {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 85px - 40px);
    background-color: #ffffff;
    position: relative;

    .canvas-container {
      display: flex;
      flex: 1;
      height: 100%;
      border: 1px solid #dfe3e8;
      border-top: none;
      overflow: hidden;
    }
  }
</style>

import { ref, onUnmounted } from 'vue';
import { Graph, Node, Edge, Cell } from '@antv/x6';
import type { WorkflowEventHandlers } from '../types/workflow';

/**
 * 工作流事件处理 Hook
 */
export function useWorkflowEvents(graph: Graph | null) {
  const selectedNode = ref<Node | null>(null);
  const selectedEdge = ref<Edge | null>(null);
  const showPropertyPanel = ref(false);

  /**
   * 防抖函数
   */
  const debounce = <T extends (...args: any[]) => any>(func: T, wait: number): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(null, args), wait);
    };
  };

  /**
   * 显示/隐藏连接桩
   */
  const togglePortsVisibility = (container: HTMLElement | null, show: boolean) => {
    if (!container) return;

    const ports = container.querySelectorAll('.x6-port-body') as NodeListOf<SVGElement>;
    ports.forEach((port) => {
      port.style.visibility = show ? 'visible' : 'hidden';
    });
  };

  /**
   * 初始化连接桩可见性控制
   */
  const initPortVisibility = (container: HTMLElement | null) => {
    if (!graph || !container) return;

    const debouncedShowPorts = debounce((show: boolean) => {
      togglePortsVisibility(container, show);
    }, 50);

    graph.on('node:mouseenter', () => {
      debouncedShowPorts(true);
    });

    graph.on('node:mouseleave', () => {
      debouncedShowPorts(false);
    });
  };

  /**
   * 默认事件处理器
   */
  const defaultEventHandlers: WorkflowEventHandlers = {
    onCellClick: ({ cell }) => {
      console.log('Cell clicked:', cell);
    },

    onNodeClick: ({ node }) => {
      selectedNode.value = node;
      selectedEdge.value = null;
      showPropertyPanel.value = true;
      console.log('Node clicked:', node);
    },

    onEdgeClick: ({ edge }) => {
      selectedEdge.value = edge;
      selectedNode.value = null;
      showPropertyPanel.value = true;
      console.log('Edge clicked:', edge);
    },

    onBlankClick: () => {
      selectedNode.value = null;
      selectedEdge.value = null;
      showPropertyPanel.value = false;
      console.log('Blank clicked');
    },

    onNodeMouseEnter: ({ node }) => {
      console.log('Node mouse enter:', node);
    },

    onNodeMouseLeave: ({ node }) => {
      console.log('Node mouse leave:', node);
    },
  };

  /**
   * 绑定图形事件
   */
  const bindGraphEvents = (container: HTMLElement | null, customHandlers?: Partial<WorkflowEventHandlers>) => {
    if (!graph) return;

    const handlers = { ...defaultEventHandlers, ...customHandlers };

    // 绑定点击事件
    graph.on('cell:click', handlers.onCellClick);
    graph.on('node:click', handlers.onNodeClick);
    graph.on('edge:click', handlers.onEdgeClick);
    graph.on('blank:click', handlers.onBlankClick);

    // 绑定鼠标事件
    graph.on('node:mouseenter', handlers.onNodeMouseEnter);
    graph.on('node:mouseleave', handlers.onNodeMouseLeave);

    // 初始化连接桩可见性
    initPortVisibility(container);
  };

  /**
   * 解绑图形事件
   */
  const unbindGraphEvents = () => {
    if (!graph) return;

    graph.off('cell:click');
    graph.off('node:click');
    graph.off('edge:click');
    graph.off('blank:click');
    graph.off('node:mouseenter');
    graph.off('node:mouseleave');
  };

  /**
   * 初始化快捷键
   */
  const initKeyboardShortcuts = (callbacks: {
    onUndo?: () => void;
    onRedo?: () => void;
    onSave?: () => void;
    onDelete?: () => void;
    onCopy?: () => void;
    onPaste?: () => void;
  }) => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              callbacks.onRedo?.();
            } else {
              callbacks.onUndo?.();
            }
            break;
          case 'y':
            e.preventDefault();
            callbacks.onRedo?.();
            break;
          case 's':
            e.preventDefault();
            callbacks.onSave?.();
            break;
          case 'c':
            e.preventDefault();
            callbacks.onCopy?.();
            break;
          case 'v':
            e.preventDefault();
            callbacks.onPaste?.();
            break;
        }
      } else if (e.key === 'Delete' || e.key === 'Backspace') {
        e.preventDefault();
        callbacks.onDelete?.();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    // 返回清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  };

  /**
   * 获取选中的节点数据
   */
  const getSelectedNodeData = () => {
    if (!selectedNode.value) return null;

    return {
      id: selectedNode.value.id,
      shape: selectedNode.value.shape,
      position: selectedNode.value.getPosition(),
      size: selectedNode.value.getSize(),
      attrs: selectedNode.value.getAttrs(),
      data: selectedNode.value.getData(),
    };
  };

  /**
   * 更新选中节点的属性
   */
  const updateSelectedNodeData = (data: Record<string, any>) => {
    if (!selectedNode.value) return;

    selectedNode.value.setData(data);

    // 如果有名称属性，更新节点文本
    if (data.name) {
      selectedNode.value.attr('text/text', data.name);
    }
  };

  /**
   * 获取选中的边数据
   */
  const getSelectedEdgeData = () => {
    if (!selectedEdge.value) return null;

    return {
      id: selectedEdge.value.id,
      source: selectedEdge.value.getSource(),
      target: selectedEdge.value.getTarget(),
      attrs: selectedEdge.value.getAttrs(),
      labels: selectedEdge.value.getLabels(),
      data: selectedEdge.value.getData(),
    };
  };

  /**
   * 更新选中边的属性
   */
  const updateSelectedEdgeData = (data: Record<string, any>) => {
    if (!selectedEdge.value) return;

    selectedEdge.value.setData(data);

    // 如果有标签属性，更新边标签
    if (data.label) {
      selectedEdge.value.setLabels([{ attrs: { text: { text: data.label } } }]);
    }
  };

  // 组件卸载时清理事件
  onUnmounted(() => {
    unbindGraphEvents();
  });

  return {
    selectedNode,
    selectedEdge,
    showPropertyPanel,
    bindGraphEvents,
    unbindGraphEvents,
    initKeyboardShortcuts,
    togglePortsVisibility,
    getSelectedNodeData,
    updateSelectedNodeData,
    getSelectedEdgeData,
    updateSelectedEdgeData,
  };
}

import { ref } from 'vue';
import { Graph } from '@antv/x6';
import type { PortConfig, LaneNodeConfig, NodeType, EdgeType } from '../types/workflow';

/**
 * 工作流节点管理 Hook
 */
export function useWorkflowNodes() {
  const laneCounter = ref(2);

  /**
   * 获取连接桩配置
   */
  const getPortsConfig = (): PortConfig => {
    return {
      groups: {
        top: {
          position: 'top',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              fill: '#fff',
              style: { visibility: 'hidden' },
            },
          },
        },
        right: {
          position: 'right',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              fill: '#fff',
              style: { visibility: 'hidden' },
            },
          },
        },
        bottom: {
          position: 'bottom',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              fill: '#fff',
              style: { visibility: 'hidden' },
            },
          },
        },
        left: {
          position: 'left',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#5F95FF',
              fill: '#fff',
              style: { visibility: 'hidden' },
            },
          },
        },
      },
      items: [{ group: 'top' }, { group: 'right' }, { group: 'bottom' }, { group: 'left' }],
    };
  };

  /**
   * 注册所有自定义节点
   */
  const registerAllNodes = () => {
    const ports = getPortsConfig();

    // 注册基础节点
    Graph.registerNode(
      'custom-rect',
      {
        inherit: 'rect',
        width: 66,
        height: 36,
        attrs: {
          body: { strokeWidth: 1, stroke: '#5F95FF', fill: '#EFF4FF' },
          text: { fontSize: 12, fill: '#262626' },
        },
        ports: { ...ports },
      },
      true
    );

    // 注册泳道节点
    Graph.registerNode(
      'lane',
      {
        inherit: 'rect',
        markup: [
          { tagName: 'rect', selector: 'body' },
          { tagName: 'rect', selector: 'name-rect' },
          { tagName: 'text', selector: 'name-text' },
        ],
        attrs: {
          body: { fill: '#FFF', stroke: '#5F95FF', strokeWidth: 1 },
          'name-rect': { width: 200, height: 30, fill: '#5F95FF', stroke: '#fff' },
          'name-text': {
            ref: 'name-rect',
            refY: 0.5,
            refX: 0.5,
            textAnchor: 'middle',
            fill: '#fff',
            fontSize: 12,
          },
        },
      },
      true
    );

    // 注册泳道内节点
    Graph.registerNode(
      'lane-rect',
      {
        inherit: 'rect',
        width: 100,
        height: 60,
        attrs: {
          body: { strokeWidth: 1, stroke: '#5F95FF', fill: '#EFF4FF' },
          text: { fontSize: 12, fill: '#262626' },
        },
        ports: { ...ports },
      },
      true
    );

    // 注册多边形节点
    Graph.registerNode(
      'lane-polygon',
      {
        inherit: 'polygon',
        width: 80,
        height: 80,
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: '#5F95FF',
            fill: '#EFF4FF',
            refPoints: '0,10 10,0 20,10 10,20',
          },
          text: { fontSize: 12, fill: '#262626' },
        },
        ports: { ...ports },
      },
      true
    );
  };

  /**
   * 注册所有自定义边
   */
  const registerAllEdges = () => {
    // 注册边
    Graph.registerEdge(
      'lane-edge',
      {
        inherit: 'edge',
        attrs: {
          line: {
            stroke: '#A2B1C3',
            strokeWidth: 2,
          },
        },
        label: {
          attrs: {
            label: {
              fill: '#A2B1C3',
              fontSize: 12,
            },
          },
        },
      },
      true
    );
  };

  /**
   * 创建泳道节点
   */
  const createLaneNode = (graph: Graph, config?: Partial<LaneNodeConfig>) => {
    if (!graph) return null;

    // 获取现有泳道
    const lanes = graph.getCells().filter((cell) => cell.shape === 'lane');

    // 计算新泳道的位置
    let x = 10;
    if (lanes.length > 0) {
      const rightmostLane = lanes.reduce((prev, current) => {
        return prev.getBBox().x + prev.getBBox().width > current.getBBox().x + current.getBBox().width ? prev : current;
      });
      x = rightmostLane.getBBox().x + rightmostLane.getBBox().width + 10;
    }

    // 创建新泳道
    laneCounter.value++;
    const defaultConfig: LaneNodeConfig = {
      id: `lane${laneCounter.value}`,
      x,
      y: 10,
      width: 300,
      height: 500,
      title: `部门${laneCounter.value}`,
      attrs: {
        body: {
          fill: '#FFF',
          stroke: '#5F95FF',
          strokeWidth: 1,
        },
        'name-rect': {
          width: 300,
          height: 30,
          fill: '#5F95FF',
          stroke: '#fff',
          strokeWidth: 1,
          x: -1,
        },
      },
    };

    const finalConfig = { ...defaultConfig, ...config };

    const newLane = graph.createNode({
      id: finalConfig.id,
      shape: 'lane',
      x: finalConfig.x,
      y: finalConfig.y,
      width: finalConfig.width,
      height: finalConfig.height,
      attrs: {
        'name-text': {
          text: finalConfig.title,
        },
        ...finalConfig.attrs,
      },
    });

    // 添加新泳道到图表
    graph.addNode(newLane);

    // 调整视图以适应新泳道
    graph.zoomToFit({ padding: 10, maxScale: 1 });

    return newLane;
  };

  /**
   * 获取基础节点列表
   */
  const getBaseNodes = (graph: Graph) => {
    return [
      graph.createNode({
        shape: 'custom-rect',
        label: '开始',
        attrs: { body: { rx: 20, ry: 26 } },
      }),
      graph.createNode({
        shape: 'custom-rect',
        label: '过程',
      }),
      graph.createNode({
        shape: 'custom-rect',
        label: '可选过程',
        attrs: { body: { rx: 6, ry: 6 } },
      }),
      graph.createNode({
        shape: 'custom-rect',
        label: '决策',
        attrs: { body: { rx: 40, ry: 20 } },
      }),
      graph.createNode({
        shape: 'custom-rect',
        label: '数据',
      }),
      graph.createNode({
        shape: 'custom-rect',
        label: '链接',
      }),
    ];
  };

  /**
   * 获取系统节点列表
   */
  const getSystemNodes = (graph: Graph) => {
    return [
      graph.createNode({
        shape: 'custom-rect',
        label: '判断',
        attrs: { body: { rx: 20, ry: 26 } },
      }),
      graph.createNode({
        shape: 'custom-rect',
        label: '流程',
      }),
      graph.createNode({
        shape: 'custom-rect',
        label: '链接节点',
        attrs: { body: { rx: 6, ry: 6 } },
      }),
    ];
  };

  /**
   * 获取泳道节点列表
   */
  const getLaneNodes = (graph: Graph) => {
    return [
      graph.createNode({
        shape: 'lane',
        width: 180,
        height: 100,
        attrs: { 'name-text': { text: '泳道' } },
      }),
      graph.createNode({
        shape: 'lane-rect',
        label: '泳道节点',
      }),
    ];
  };

  return {
    laneCounter,
    registerAllNodes,
    registerAllEdges,
    createLaneNode,
    getBaseNodes,
    getSystemNodes,
    getLaneNodes,
    getPortsConfig,
  };
}

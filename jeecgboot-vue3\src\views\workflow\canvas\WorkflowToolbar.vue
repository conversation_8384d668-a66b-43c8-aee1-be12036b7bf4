<template>
  <div class="workflow-toolbar">
    <div @click="handleUndo" class="toolbar-command" title="撤销"> 撤销 </div>

    <!-- <el-divider direction="vertical" /> -->

    <div @click="handleDelete" class="toolbar-command" title="删除"> 删除 </div>

    <!-- <el-divider direction="vertical" /> -->

    <div @click="handleSave" class="toolbar-command" title="保存"> 保存 </div>

    <div @click="handleAddLane" class="toolbar-command" title="添加泳道"> 添加泳道 </div>

    <div @click="handleGenerateJson" class="toolbar-command" title="生成JSON"> JSON </div>

    <!-- <el-divider direction="vertical" /> -->

    <div @click="handleCopy" class="toolbar-command" title="复制"> 复制 </div>

    <div @click="handlePaste" class="toolbar-command" title="粘贴"> 粘贴 </div>

    <!-- <el-divider direction="vertical" /> -->

    <div @click="handleZoomIn" class="toolbar-command" title="放大"> 放大 </div>

    <div @click="handleZoomOut" class="toolbar-command" title="缩小"> 缩小 </div>

    <div @click="handleZoomToFit" class="toolbar-command" title="适应画布"> 适应 </div>
  </div>
</template>

<script setup lang="ts">
  import { Graph } from '@antv/x6';
  import { useMessage } from '/@/hooks/web/useMessage';
  import type { ToolbarAction } from './types/workflow';

  interface Props {
    graph: Graph | null;
    loading?: boolean;
  }

  interface Emits {
    (e: 'action', action: ToolbarAction, data?: any): void;
    (e: 'save'): void;
    (e: 'addLane'): void;
    (e: 'generateJson'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
  });

  const emit = defineEmits<Emits>();
  const { createMessage } = useMessage();

  /**
   * 撤销操作
   */
  const handleUndo = () => {
    if (!props.graph) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    if (props.graph.canUndo()) {
      props.graph.undo();
      emit('action', ToolbarAction.UNDO);
    } else {
      createMessage.info('没有可撤销的操作');
    }
  };

  /**
   * 重做操作
   */
  const handleRedo = () => {
    if (!props.graph) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    if (props.graph.canRedo()) {
      props.graph.redo();
      emit('action', ToolbarAction.REDO);
    } else {
      createMessage.info('没有可重做的操作');
    }
  };

  /**
   * 删除选中元素
   */
  const handleDelete = () => {
    if (!props.graph) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    const cells = props.graph.getSelectedCells();
    if (cells.length) {
      props.graph.removeCells(cells);
      emit('action', ToolbarAction.DELETE, { count: cells.length });
      createMessage.success(`已删除 ${cells.length} 个元素`);
    } else {
      createMessage.info('请先选择要删除的元素');
    }
  };

  /**
   * 复制选中元素
   */
  const handleCopy = () => {
    if (!props.graph) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    const cells = props.graph.getSelectedCells();
    if (cells.length) {
      props.graph.copy(cells);
      createMessage.success(`已复制 ${cells.length} 个元素`);
    } else {
      createMessage.info('请先选择要复制的元素');
    }
  };

  /**
   * 粘贴元素
   */
  const handlePaste = () => {
    if (!props.graph) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    if (!props.graph.isClipboardEmpty()) {
      const cells = props.graph.paste({ offset: 32 });
      props.graph.cleanSelection();
      props.graph.select(cells);
      createMessage.success(`已粘贴 ${cells.length} 个元素`);
    } else {
      createMessage.info('剪贴板为空');
    }
  };

  /**
   * 放大画布
   */
  const handleZoomIn = () => {
    if (!props.graph) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    const currentZoom = props.graph.zoom();
    if (currentZoom < 3) {
      props.graph.zoom(0.1);
    } else {
      createMessage.info('已达到最大缩放比例');
    }
  };

  /**
   * 缩小画布
   */
  const handleZoomOut = () => {
    if (!props.graph) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    const currentZoom = props.graph.zoom();
    if (currentZoom > 0.5) {
      props.graph.zoom(-0.1);
    } else {
      createMessage.info('已达到最小缩放比例');
    }
  };

  /**
   * 适应画布
   */
  const handleZoomToFit = () => {
    if (!props.graph) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    props.graph.zoomToFit({ padding: 10, maxScale: 1 });
    createMessage.success('已适应画布大小');
  };

  /**
   * 保存操作
   */
  const handleSave = () => {
    if (props.loading) {
      createMessage.warning('正在保存中，请稍候...');
      return;
    }

    emit('save');
    emit('action', ToolbarAction.SAVE);
  };

  /**
   * 添加泳道
   */
  const handleAddLane = () => {
    emit('addLane');
    emit('action', ToolbarAction.ADD_LANE);
  };

  /**
   * 生成JSON
   */
  const handleGenerateJson = () => {
    emit('generateJson');
    emit('action', ToolbarAction.GENERATE_JSON);
  };

  // 暴露方法给父组件
  defineExpose({
    handleUndo,
    handleRedo,
    handleDelete,
    handleCopy,
    handlePaste,
    handleZoomIn,
    handleZoomOut,
    handleZoomToFit,
  });
</script>

<style lang="less" scoped>
  .workflow-toolbar {
    padding: 8px;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.45);
    background-color: #ffffff;
    border-bottom: 1px solid #dfe3e8;

    .toolbar-command {
      display: inline-block;
      min-width: 60px;
      height: 30px;
      margin: 0 6px;
      padding: 6px 12px;
      text-align: center;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s;
      user-select: none;

      &:hover {
        background-color: #f5f5f5;
        color: rgba(0, 0, 0, 0.85);
      }

      &:active {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      &.disabled {
        color: rgba(0, 0, 0, 0.25);
        cursor: not-allowed;

        &:hover {
          background-color: transparent;
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }

    .el-divider {
      margin: 0 8px;
      height: 20px;
    }
  }
</style>

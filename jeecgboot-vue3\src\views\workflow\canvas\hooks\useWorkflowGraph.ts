import { ref, onUnmounted } from 'vue';
import { Graph, Shape } from '@antv/x6';
import { Transform } from '@antv/x6-plugin-transform';
import { Selection } from '@antv/x6-plugin-selection';
import { Snapline } from '@antv/x6-plugin-snapline';
import type { WorkflowGraphInstance, SwimLaneData } from '../types/workflow';

/**
 * 工作流图形实例管理 Hook
 */
export function useWorkflowGraph(): WorkflowGraphInstance {
  const graph = ref<Graph | null>(null);

  /**
   * 初始化图形实例
   */
  const initGraph = (container: HTMLElement) => {
    if (!container) {
      console.error('Graph container is required');
      return;
    }

    // 强制设置容器尺寸
    container.style.width = 'calc(100% - 200px)';
    container.style.height = '100%';

    // 创建图表实例
    graph.value = new Graph({
      container,
      width: container.clientWidth,
      height: container.clientHeight,
      grid: {
        size: 10,
        visible: true,
        type: 'dot',
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 3,
      },
      connecting: {
        router: 'manhattan',
        connector: {
          name: 'rounded',
          args: { radius: 8 },
        },
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        snap: { radius: 20 },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: { name: 'block', width: 12, height: 8 },
              },
            },
            zIndex: 0,
          });
        },
        validateConnection({ targetMagnet }) {
          return !!targetMagnet;
        },
      },
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: { attrs: { fill: '#5F95FF', stroke: '#5F95FF' } },
        },
      },
      translating: {
        restrict(cellView: any) {
          const cell = cellView.cell;
          const parentId = cell.prop('parent');
          if (parentId) {
            const parentNode = graph.value?.getCellById(parentId);
            if (parentNode) {
              return parentNode.getBBox().moveAndExpand({
                x: 0,
                y: 30,
                width: 0,
                height: -30,
              });
            }
          }
          return cell.getBBox();
        },
      },
    });

    // 初始化插件
    if (graph.value) {
      graph.value
        .use(new Transform({ resizing: true, rotating: true }))
        .use(new Selection({ rubberband: true, showNodeSelectionBox: true }))
        .use(new Snapline());
    }
  };

  /**
   * 销毁图形实例
   */
  const destroyGraph = () => {
    if (graph.value) {
      graph.value.dispose();
      graph.value = null;
    }
  };

  /**
   * 获取图形数据
   */
  const getGraphData = () => {
    if (!graph.value) return null;
    return graph.value.toJSON();
  };

  /**
   * 加载图形数据
   */
  const loadGraphData = (data: SwimLaneData) => {
    if (!graph.value) return;

    try {
      const cells: any[] = [];
      const laneMap = new Map();

      // 1. 先创建泳道节点
      data.lanes.forEach((lane) => {
        const laneNode = graph.value!.createNode({
          id: lane.id,
          shape: 'lane',
          x: lane.x,
          y: lane.y,
          width: lane.width,
          height: lane.height,
          attrs: {
            'name-text': {
              text: lane.title,
            },
            ...lane.attrs,
          },
        });
        cells.push(laneNode);

        // 存储泳道位置信息用于坐标转换
        laneMap.set(lane.id, {
          x: lane.x,
          y: lane.y,
          width: lane.width,
          height: lane.height,
        });
      });

      // 2. 处理节点
      data.nodes.forEach((node) => {
        const parentLane = laneMap.get(node.parent);
        let relativeX = node.x;
        const relativeY = node.y;

        // 转换为相对坐标
        if (parentLane) {
          relativeX = parentLane.x;
        }

        const nodeItem = graph.value!.createNode({
          id: node.id,
          shape: node.shape || 'lane-rect',
          x: relativeX,
          y: relativeY,
          width: node.width,
          height: node.height,
          attrs: {
            text: {
              text: node.label,
            },
            ...node.attrs,
          },
          parent: node.parent,
        });
        cells.push(nodeItem);
      });

      // 3. 处理边
      data.edges.forEach((edge) => {
        const edgeItem = graph.value!.createEdge({
          id: edge.id,
          shape: edge.shape || 'lane-edge',
          source: { cell: edge.source },
          target: { cell: edge.target },
          labels: edge.labels,
          attrs: edge.attrs,
        });
        cells.push(edgeItem);
      });

      graph.value.resetCells(cells);
      graph.value.zoomToFit({ padding: 10, maxScale: 1 });
    } catch (error) {
      console.error('加载图形数据失败:', error);
      throw error;
    }
  };

  // 组件卸载时清理资源
  onUnmounted(() => {
    destroyGraph();
  });

  return {
    graph: graph.value,
    initGraph,
    destroyGraph,
    getGraphData,
    loadGraphData,
  };
}

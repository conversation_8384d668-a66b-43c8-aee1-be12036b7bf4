<template>
  <div class="workflow-sidebar" :class="{ 'sidebar-collapsed': collapsed }">
    <!-- 折叠/展开按钮 -->
    <div class="sidebar-toggle" @click="toggleCollapse">
      <Icon :icon="collapsed ? 'ant-design:right-outlined' : 'ant-design:left-outlined'" />
    </div>

    <!-- 侧边栏内容 -->
    <div class="sidebar-content" v-show="!collapsed">
      <!-- 节点属性面板 -->
      <div v-if="selectedNode" class="property-panel">
        <div class="panel-header">
          <h3>节点属性</h3>
          <div class="node-type">{{ getNodeTypeLabel(selectedNode.shape) }}</div>
        </div>

        <div class="panel-body">
          <a-form layout="vertical" :model="nodeProperties">
            <a-form-item label="节点名称">
              <a-input v-model:value="nodeProperties.name" placeholder="请输入节点名称" @change="updateNodeProperties" />
            </a-form-item>

            <a-form-item label="节点类型">
              <a-select v-model:value="nodeProperties.type" placeholder="请选择节点类型" @change="updateNodeProperties">
                <a-select-option value="start">开始节点</a-select-option>
                <a-select-option value="process">处理节点</a-select-option>
                <a-select-option value="decision">决策节点</a-select-option>
                <a-select-option value="end">结束节点</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="委托人">
              <a-input v-model:value="nodeProperties.assignee" placeholder="请输入委托人" @change="updateNodeProperties" />
            </a-form-item>

            <a-form-item label="委托机构">
              <a-input v-model:value="nodeProperties.assigneeOrgCode" placeholder="请输入委托机构代码" @change="updateNodeProperties" />
            </a-form-item>

            <a-form-item label="回调接口">
              <a-input v-model:value="nodeProperties.handler" placeholder="请输入回调接口" @change="updateNodeProperties" />
            </a-form-item>

            <a-form-item label="节点描述">
              <a-textarea v-model:value="nodeProperties.description" placeholder="请输入节点描述" :rows="3" @change="updateNodeProperties" />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 边属性面板 -->
      <div v-else-if="selectedEdge" class="property-panel">
        <div class="panel-header">
          <h3>连线属性</h3>
          <div class="edge-type">连接线</div>
        </div>

        <div class="panel-body">
          <a-form layout="vertical" :model="edgeProperties">
            <a-form-item label="连线名称">
              <a-input v-model:value="edgeProperties.label" placeholder="请输入连线名称" @change="updateEdgeProperties" />
            </a-form-item>

            <a-form-item label="条件表达式">
              <a-textarea v-model:value="edgeProperties.condition" placeholder="请输入条件表达式" :rows="3" @change="updateEdgeProperties" />
            </a-form-item>

            <a-form-item label="连线描述">
              <a-textarea v-model:value="edgeProperties.description" placeholder="请输入连线描述" :rows="3" @change="updateEdgeProperties" />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 画布属性面板 -->
      <div v-else class="property-panel">
        <div class="panel-header">
          <h3>画布属性</h3>
        </div>

        <div class="panel-body">
          <a-form layout="vertical" :model="canvasProperties">
            <a-form-item label="工作流名称">
              <a-input v-model:value="canvasProperties.name" placeholder="请输入工作流名称" @change="updateCanvasProperties" />
            </a-form-item>

            <a-form-item label="业务标识">
              <a-input v-model:value="canvasProperties.businessKey" placeholder="请输入业务标识" @change="updateCanvasProperties" />
            </a-form-item>

            <a-form-item label="版本号">
              <a-input v-model:value="canvasProperties.version" placeholder="请输入版本号" @change="updateCanvasProperties" />
            </a-form-item>

            <a-form-item label="工作流描述">
              <a-textarea v-model:value="canvasProperties.description" placeholder="请输入工作流描述" :rows="4" @change="updateCanvasProperties" />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-panel">
        <div class="panel-header">
          <h3>统计信息</h3>
        </div>

        <div class="panel-body">
          <div class="stats-item">
            <span class="stats-label">节点数量:</span>
            <span class="stats-value">{{ stats.nodeCount }}</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">连线数量:</span>
            <span class="stats-value">{{ stats.edgeCount }}</span>
          </div>
          <div class="stats-item">
            <span class="stats-label">泳道数量:</span>
            <span class="stats-value">{{ stats.laneCount }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, watch, computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import type { NodePropertyData } from './types/workflow';

  interface Props {
    selectedNode?: any;
    selectedEdge?: any;
    canvasData?: any;
    width?: number;
  }

  interface Emits {
    (e: 'nodePropertiesChange', properties: NodePropertyData): void;
    (e: 'edgePropertiesChange', properties: any): void;
    (e: 'canvasPropertiesChange', properties: any): void;
    (e: 'widthChange', width: number): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    width: 300,
  });

  const emit = defineEmits<Emits>();

  const collapsed = ref(false);

  // 节点属性
  const nodeProperties = reactive<NodePropertyData>({
    name: '',
    assignee: '',
    assigneeOrgCode: '',
    handler: '',
    type: '',
    description: '',
  });

  // 边属性
  const edgeProperties = reactive({
    label: '',
    condition: '',
    description: '',
  });

  // 画布属性
  const canvasProperties = reactive({
    name: '',
    businessKey: '',
    version: '',
    description: '',
  });

  // 统计信息
  const stats = computed(() => {
    if (!props.canvasData) {
      return { nodeCount: 0, edgeCount: 0, laneCount: 0 };
    }

    const cells = props.canvasData.cells || [];
    const nodeCount = cells.filter((cell: any) => cell.shape && !cell.source && !cell.target).length;
    const edgeCount = cells.filter((cell: any) => cell.source && cell.target).length;
    const laneCount = cells.filter((cell: any) => cell.shape === 'lane').length;

    return { nodeCount, edgeCount, laneCount };
  });

  /**
   * 获取节点类型标签
   */
  const getNodeTypeLabel = (shape: string) => {
    const typeMap: Record<string, string> = {
      'custom-rect': '基础节点',
      lane: '泳道',
      'lane-rect': '泳道节点',
      'lane-polygon': '多边形节点',
    };
    return typeMap[shape] || '未知类型';
  };

  /**
   * 切换折叠状态
   */
  const toggleCollapse = () => {
    collapsed.value = !collapsed.value;
    const newWidth = collapsed.value ? 40 : props.width;
    emit('widthChange', newWidth);
  };

  /**
   * 更新节点属性
   */
  const updateNodeProperties = () => {
    emit('nodePropertiesChange', { ...nodeProperties });
  };

  /**
   * 更新边属性
   */
  const updateEdgeProperties = () => {
    emit('edgePropertiesChange', { ...edgeProperties });
  };

  /**
   * 更新画布属性
   */
  const updateCanvasProperties = () => {
    emit('canvasPropertiesChange', { ...canvasProperties });
  };

  // 监听选中节点变化
  watch(
    () => props.selectedNode,
    (node) => {
      if (node) {
        const data = node.data || {};
        Object.assign(nodeProperties, {
          name: data.name || node.label || '',
          assignee: data.assignee || '',
          assigneeOrgCode: data.assigneeOrgCode || '',
          handler: data.handler || '',
          type: data.type || '',
          description: data.description || '',
        });
      }
    },
    { immediate: true }
  );

  // 监听选中边变化
  watch(
    () => props.selectedEdge,
    (edge) => {
      if (edge) {
        const data = edge.data || {};
        const labels = edge.labels || [];
        Object.assign(edgeProperties, {
          label: labels.length > 0 ? labels[0].attrs?.text?.text || '' : '',
          condition: data.condition || '',
          description: data.description || '',
        });
      }
    },
    { immediate: true }
  );

  // 监听画布数据变化
  watch(
    () => props.canvasData,
    (data) => {
      if (data) {
        Object.assign(canvasProperties, {
          name: data.name || '',
          businessKey: data.businessKey || '',
          version: data.version || '',
          description: data.description || '',
        });
      }
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .workflow-sidebar {
    width: 300px;
    height: 100%;
    background-color: #fff;
    border-left: 1px solid #dfe3e8;
    position: relative;
    transition: width 0.3s ease;

    &.sidebar-collapsed {
      width: 40px;
    }

    .sidebar-toggle {
      position: absolute;
      top: 50%;
      left: -12px;
      width: 24px;
      height: 24px;
      background-color: #fff;
      border: 1px solid #dfe3e8;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10;
      transform: translateY(-50%);
      transition: all 0.3s;

      &:hover {
        background-color: #f5f5f5;
        border-color: #1890ff;
        color: #1890ff;
      }
    }

    .sidebar-content {
      height: 100%;
      overflow-y: auto;
      padding: 0;
    }

    .property-panel,
    .stats-panel {
      border-bottom: 1px solid #f0f0f0;

      .panel-header {
        padding: 16px;
        background-color: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }

        .node-type,
        .edge-type {
          font-size: 12px;
          color: #666;
          background-color: #e6f7ff;
          padding: 2px 8px;
          border-radius: 12px;
        }
      }

      .panel-body {
        padding: 16px;

        .ant-form-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .ant-form-item-label {
          padding-bottom: 4px;

          label {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .stats-panel {
      .stats-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .stats-label {
          font-size: 12px;
          color: #666;
        }

        .stats-value {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  // 滚动条样式
  .sidebar-content::-webkit-scrollbar {
    width: 6px;
  }

  .sidebar-content::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .sidebar-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .sidebar-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
</style>

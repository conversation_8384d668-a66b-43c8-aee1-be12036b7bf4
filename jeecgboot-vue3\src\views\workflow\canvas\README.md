# 工作流画布组件

这是一个基于 AntV X6 的工作流设计器，已经进行了模块化重构，提供了更好的可维护性和扩展性。

## 目录结构

```
canvas/
├── WorkflowDefineCanvas.vue    # 主容器组件
├── WorkflowCanvas.vue          # 画布组件
├── WorkflowStencil.vue         # 组件面板
├── WorkflowToolbar.vue         # 工具栏组件
├── WorkflowSidebar.vue         # 属性面板
├── hooks/                      # 可复用逻辑
│   ├── useWorkflowGraph.ts     # 图形实例管理
│   ├── useWorkflowNodes.ts     # 节点管理
│   └── useWorkflowEvents.ts    # 事件处理
├── types/                      # 类型定义
│   └── workflow.ts             # 工作流相关类型
├── index.ts                    # 导出文件
└── README.md                   # 说明文档
```

## 组件说明

### WorkflowDefineCanvas.vue
主容器组件，负责：
- 组件组合和布局
- 数据流管理
- 与后端 API 交互
- 事件协调

### WorkflowCanvas.vue
画布组件，负责：
- X6 Graph 实例管理
- 画布渲染和交互
- 数据加载和导出
- 缩放、平移等操作

### WorkflowStencil.vue
组件面板，负责：
- 节点模板管理
- 拖拽源配置
- 分组管理

### WorkflowToolbar.vue
工具栏组件，负责：
- 操作按钮（撤销、删除、保存等）
- 快捷操作
- 状态反馈

### WorkflowSidebar.vue
属性面板，负责：
- 节点/边属性编辑
- 画布属性配置
- 统计信息显示

## Hooks 说明

### useWorkflowGraph
图形实例管理 Hook，提供：
- 图形初始化和销毁
- 数据加载和获取
- 基础配置管理

### useWorkflowNodes
节点管理 Hook，提供：
- 节点类型注册
- 泳道创建
- 节点模板生成

### useWorkflowEvents
事件处理 Hook，提供：
- 事件绑定和解绑
- 快捷键支持
- 交互反馈

## 使用示例

```vue
<template>
  <WorkflowDefineCanvas />
</template>

<script setup lang="ts">
import { WorkflowDefineCanvas } from '@/views/workflow/canvas';
</script>
```

## 特性

- ✅ 模块化架构，职责分离
- ✅ TypeScript 类型安全
- ✅ 可复用的 Hooks
- ✅ 响应式数据管理
- ✅ 事件驱动架构
- ✅ 支持泳道式工作流
- ✅ 丰富的交互功能
- ✅ 属性面板编辑
- ✅ 工具栏操作

## 扩展指南

### 添加新节点类型
1. 在 `useWorkflowNodes.ts` 中注册新节点
2. 在 `WorkflowStencil.vue` 中添加到对应分组
3. 在 `types/workflow.ts` 中添加类型定义

### 添加新工具栏操作
1. 在 `WorkflowToolbar.vue` 中添加按钮
2. 在主组件中添加对应的事件处理函数
3. 在 `types/workflow.ts` 中添加操作类型

### 自定义属性面板
1. 在 `WorkflowSidebar.vue` 中添加新的表单项
2. 在事件处理中添加属性更新逻辑
3. 在类型定义中添加新属性

## 注意事项

1. 确保 X6 相关依赖已正确安装
2. 组件间通信使用 props/emit 模式
3. 复杂状态管理可考虑使用 Pinia
4. 保持类型定义的完整性和准确性

import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '业务key',
    align: "center",
    dataIndex: 'businessKey'
  },
  {
    title: '工作流名称',
    align: "center",
    dataIndex: 'name'
  },
  {
    title: '创建人',
    align: "center",
    dataIndex: 'createBy'
  },
  {
    title: '创建日期',
    align: "center",
    dataIndex: 'createTime'
  },
  {
    title: '更新人',
    align: "center",
    dataIndex: 'updateBy'
  },
  {
    title: '更新日期',
    align: "center",
    dataIndex: 'updateTime'
  },
  // {
  //   title: '工作流定义（前端展示）',
  //   align: "center",
  //   dataIndex: 'definitionView'
  // },
  {
    title: '是否生效',
    align: "center",
    dataIndex: 'isEffective_dictText',

  }
];

// 高级查询数据
export const superQuerySchema = {
  createBy: {title: '创建人',order: 0,view: 'text', type: 'string',},
  createTime: {title: '创建日期',order: 1,view: 'datetime', type: 'string',},
  updateBy: {title: '更新人',order: 2,view: 'text', type: 'string',},
  updateTime: {title: '更新日期',order: 3,view: 'datetime', type: 'string',},
  name: {title: '工作流名称',order: 4,view: 'text', type: 'string',},
  definitionView: {title: '工作流定义（前端展示）',order: 5,view: 'text', type: 'string',},
  businessKey: {title: '业务key',order: 6,view: 'text', type: 'string',},
};

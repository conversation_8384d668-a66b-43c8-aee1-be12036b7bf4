<template>
  <div class="basic-usage-example">
    <h2>基础使用示例</h2>

    <!-- 使用重构后的工作流画布组件 -->
    <WorkflowDefineCanvas />
  </div>
</template>

<script setup lang="ts">
  import WorkflowDefineCanvas from '../WorkflowDefineCanvas.vue';
</script>

<style lang="less" scoped>
  .basic-usage-example {
    width: 100%;
    height: 100vh;

    h2 {
      padding: 16px;
      margin: 0;
      background-color: #f5f5f5;
      border-bottom: 1px solid #ddd;
    }
  }
</style>

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import WorkflowCanvas from '../WorkflowCanvas.vue';
import type { SwimLaneData } from '../types/workflow';

// Mock X6
vi.mock('@antv/x6', () => ({
  Graph: vi.fn().mockImplementation(() => ({
    dispose: vi.fn(),
    toJSON: vi.fn(() => ({ cells: [] })),
    resetCells: vi.fn(),
    zoomToFit: vi.fn(),
    on: vi.fn(),
    off: vi.fn(),
    setInteracting: vi.fn(),
  })),
  Shape: {
    Edge: vi.fn(),
  },
}));

vi.mock('@antv/x6-plugin-transform', () => ({
  Transform: vi.fn(),
}));

vi.mock('@antv/x6-plugin-selection', () => ({
  Selection: vi.fn(),
}));

vi.mock('@antv/x6-plugin-snapline', () => ({
  Snapline: vi.fn(),
}));

describe('WorkflowCanvas', () => {
  let wrapper: any;

  beforeEach(() => {
    wrapper = mount(WorkflowCanvas, {
      props: {
        width: '100%',
        height: '100%',
      },
    });
  });

  it('should render correctly', () => {
    expect(wrapper.find('.workflow-canvas').exists()).toBe(true);
  });

  it('should emit ready event when graph is initialized', async () => {
    // 等待组件挂载完成
    await wrapper.vm.$nextTick();

    // 检查是否触发了 ready 事件
    expect(wrapper.emitted('ready')).toBeTruthy();
  });

  it('should handle data loading', async () => {
    const testData: SwimLaneData = {
      lanes: [
        {
          id: 'lane1',
          x: 10,
          y: 10,
          width: 300,
          height: 500,
          title: '测试泳道',
        },
      ],
      nodes: [
        {
          id: 'node1',
          x: 50,
          y: 50,
          width: 100,
          height: 60,
          label: '测试节点',
          shape: 'lane-rect',
        },
      ],
      edges: [
        {
          id: 'edge1',
          source: 'node1',
          target: 'node2',
        },
      ],
    };

    await wrapper.setProps({ data: testData });
    await wrapper.vm.$nextTick();

    // 验证数据是否正确加载
    expect(wrapper.vm.isReady).toBe(true);
  });

  it('should handle readonly mode', async () => {
    await wrapper.setProps({ readonly: true });
    await wrapper.vm.$nextTick();

    // 验证只读模式是否生效
    expect(wrapper.props('readonly')).toBe(true);
  });

  it('should expose correct methods', () => {
    const exposedMethods = ['loadData', 'getData', 'clearCanvas', 'zoomToFit', 'setZoom', 'getZoom', 'centerContent', 'exportImage'];

    exposedMethods.forEach((method) => {
      expect(typeof wrapper.vm[method]).toBe('function');
    });
  });

  it('should handle node selection', async () => {
    const mockNode = {
      id: 'test-node',
      shape: 'lane-rect',
      getPosition: () => ({ x: 100, y: 100 }),
      getSize: () => ({ width: 100, height: 60 }),
      getData: () => ({ name: 'Test Node' }),
    };

    // 模拟节点点击
    wrapper.vm.handleNodeClick(mockNode);

    expect(wrapper.emitted('nodeClick')).toBeTruthy();
    expect(wrapper.emitted('nodeClick')[0][0]).toEqual({
      id: 'test-node',
      shape: 'lane-rect',
      position: { x: 100, y: 100 },
      size: { width: 100, height: 60 },
      data: { name: 'Test Node' },
    });
  });

  it('should handle edge selection', async () => {
    const mockEdge = {
      id: 'test-edge',
      getSource: () => ({ cell: 'node1' }),
      getTarget: () => ({ cell: 'node2' }),
      getData: () => ({ label: 'Test Edge' }),
    };

    // 模拟边点击
    wrapper.vm.handleEdgeClick(mockEdge);

    expect(wrapper.emitted('edgeClick')).toBeTruthy();
    expect(wrapper.emitted('edgeClick')[0][0]).toEqual({
      id: 'test-edge',
      source: { cell: 'node1' },
      target: { cell: 'node2' },
      data: { label: 'Test Edge' },
    });
  });

  it('should handle blank click', async () => {
    wrapper.vm.handleBlankClick();

    expect(wrapper.emitted('blankClick')).toBeTruthy();
  });
});

<template>
  <div class="workflow-canvas" ref="canvasContainerRef">
    <!-- X6 Graph 画布将在这里渲染 -->
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import { Graph } from '@antv/x6';
  import { useWorkflowGraph } from './hooks/useWorkflowGraph';
  import { useWorkflowEvents } from './hooks/useWorkflowEvents';
  import type { SwimLaneData, WorkflowEventHandlers } from './types/workflow';

  interface Props {
    width?: string | number;
    height?: string | number;
    data?: SwimLaneData | null;
    readonly?: boolean;
  }

  interface Emits {
    (e: 'ready', graph: Graph): void;
    (e: 'dataChange', data: any): void;
    (e: 'selectionChange', selection: { nodes: any[]; edges: any[] }): void;
    (e: 'nodeClick', node: any): void;
    (e: 'edgeClick', edge: any): void;
    (e: 'blankClick'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    width: '100%',
    height: '100%',
    readonly: false,
  });

  const emit = defineEmits<Emits>();

  const canvasContainerRef = ref<HTMLElement | null>(null);
  const isReady = ref(false);

  // 使用图形管理 Hook
  const { graph, initGraph, destroyGraph, getGraphData, loadGraphData } = useWorkflowGraph();

  // 使用事件处理 Hook
  const {
    selectedNode,
    selectedEdge,
    showPropertyPanel,
    bindGraphEvents,
    unbindGraphEvents,
    initKeyboardShortcuts,
    getSelectedNodeData,
    updateSelectedNodeData,
    getSelectedEdgeData,
    updateSelectedEdgeData,
  } = useWorkflowEvents(graph);

  /**
   * 初始化画布
   */
  const initCanvas = async () => {
    if (!canvasContainerRef.value) {
      console.error('Canvas container not found');
      return;
    }

    try {
      // 等待 DOM 更新
      await nextTick();

      // 设置容器样式
      const container = canvasContainerRef.value;
      container.style.width = typeof props.width === 'number' ? `${props.width}px` : props.width;
      container.style.height = typeof props.height === 'number' ? `${props.height}px` : props.height;

      // 初始化图形实例
      initGraph(container);

      if (graph) {
        // 自定义事件处理器
        const customEventHandlers: Partial<WorkflowEventHandlers> = {
          onNodeClick: ({ node }) => {
            emit('nodeClick', {
              id: node.id,
              shape: node.shape,
              position: node.getPosition(),
              size: node.getSize(),
              data: node.getData(),
            });
          },
          onEdgeClick: ({ edge }) => {
            emit('edgeClick', {
              id: edge.id,
              source: edge.getSource(),
              target: edge.getTarget(),
              data: edge.getData(),
            });
          },
          onBlankClick: () => {
            emit('blankClick');
          },
        };

        // 绑定事件
        bindGraphEvents(container, customEventHandlers);

        // 初始化快捷键（如果不是只读模式）
        if (!props.readonly) {
          initKeyboardShortcuts({
            onUndo: () => graph?.canUndo() && graph.undo(),
            onRedo: () => graph?.canRedo() && graph.redo(),
            onDelete: () => {
              const cells = graph?.getSelectedCells();
              if (cells?.length) {
                graph?.removeCells(cells);
              }
            },
            onCopy: () => {
              const cells = graph?.getSelectedCells();
              if (cells?.length) {
                graph?.copy(cells);
              }
            },
            onPaste: () => {
              if (!graph?.isClipboardEmpty()) {
                const cells = graph?.paste({ offset: 32 });
                graph?.cleanSelection();
                graph?.select(cells);
              }
            },
          });
        }

        // 监听数据变化
        graph.on('cell:added', () => {
          emit('dataChange', getGraphData());
        });

        graph.on('cell:removed', () => {
          emit('dataChange', getGraphData());
        });

        graph.on('cell:changed', () => {
          emit('dataChange', getGraphData());
        });

        // 监听选择变化
        graph.on('selection:changed', ({ selected, removed }) => {
          const nodes = selected
            .filter((cell) => cell.isNode())
            .map((node) => ({
              id: node.id,
              data: node.getData(),
            }));
          const edges = selected
            .filter((cell) => cell.isEdge())
            .map((edge) => ({
              id: edge.id,
              data: edge.getData(),
            }));

          emit('selectionChange', { nodes, edges });
        });

        isReady.value = true;
        emit('ready', graph);

        // 如果有初始数据，加载它
        if (props.data) {
          loadData(props.data);
        }
      }
    } catch (error) {
      console.error('初始化画布失败:', error);
    }
  };

  /**
   * 加载数据到画布
   */
  const loadData = (data: SwimLaneData) => {
    if (!graph || !data) return;

    try {
      loadGraphData(data);
    } catch (error) {
      console.error('加载数据失败:', error);
      throw error;
    }
  };

  /**
   * 获取画布数据
   */
  const getData = () => {
    return getGraphData();
  };

  /**
   * 清空画布
   */
  const clearCanvas = () => {
    if (!graph) return;
    graph.clearCells();
  };

  /**
   * 缩放到适合大小
   */
  const zoomToFit = (options?: { padding?: number; maxScale?: number }) => {
    if (!graph) return;
    graph.zoomToFit({ padding: 10, maxScale: 1, ...options });
  };

  /**
   * 设置缩放比例
   */
  const setZoom = (scale: number) => {
    if (!graph) return;
    graph.scale(scale);
  };

  /**
   * 获取当前缩放比例
   */
  const getZoom = () => {
    if (!graph) return 1;
    return graph.zoom();
  };

  /**
   * 居中显示
   */
  const centerContent = () => {
    if (!graph) return;
    graph.centerContent();
  };

  /**
   * 导出为图片
   */
  const exportImage = (type: 'PNG' | 'JPEG' | 'SVG' = 'PNG') => {
    if (!graph) return null;

    return new Promise((resolve, reject) => {
      try {
        if (type === 'SVG') {
          const svgData = graph.toSVG();
          resolve(svgData);
        } else {
          graph.toPNG(
            (dataUri: string) => {
              resolve(dataUri);
            },
            {
              padding: { top: 20, right: 20, bottom: 20, left: 20 },
              quality: type === 'JPEG' ? 0.8 : 1,
            }
          );
        }
      } catch (error) {
        reject(error);
      }
    });
  };

  // 监听数据变化
  watch(
    () => props.data,
    (newData) => {
      if (newData && isReady.value) {
        loadData(newData);
      }
    },
    { deep: true }
  );

  // 监听只读模式变化
  watch(
    () => props.readonly,
    (readonly) => {
      if (!graph) return;

      // 设置图形的交互性
      graph.setInteracting(!readonly);
    }
  );

  onMounted(() => {
    initCanvas();
  });

  onUnmounted(() => {
    unbindGraphEvents();
    destroyGraph();
  });

  // 暴露方法给父组件
  defineExpose({
    graph,
    isReady,
    selectedNode,
    selectedEdge,
    showPropertyPanel,
    loadData,
    getData,
    clearCanvas,
    zoomToFit,
    setZoom,
    getZoom,
    centerContent,
    exportImage,
    getSelectedNodeData,
    updateSelectedNodeData,
    getSelectedEdgeData,
    updateSelectedEdgeData,
  });
</script>

<style lang="less" scoped>
  .workflow-canvas {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #fff;
    overflow: hidden;

    // X6 样式覆盖
    :deep(.x6-widget-transform) {
      margin: -1px 0 0 -1px;
      padding: 0px;
      border: 1px solid #239edd;
    }

    :deep(.x6-widget-transform > div) {
      border: 1px solid #239edd;
    }

    :deep(.x6-widget-transform > div:hover) {
      background-color: #3dafe4;
    }

    :deep(.x6-widget-transform-active-handle) {
      background-color: #3dafe4;
    }

    :deep(.x6-widget-transform-resize) {
      border-radius: 0;
    }

    :deep(.x6-widget-selection-inner) {
      border: 1px solid #239edd;
    }

    :deep(.x6-widget-selection-box) {
      opacity: 0;
    }

    :deep(.x6-node:hover .topic-image) {
      visibility: visible;
    }

    :deep(.x6-node-selected rect) {
      stroke-width: 2px;
    }

    // 连接桩样式
    :deep(.x6-port-body) {
      transition: all 0.3s;
    }

    :deep(.x6-port-body:hover) {
      stroke-width: 2px;
      stroke: #1890ff;
    }
  }
</style>
